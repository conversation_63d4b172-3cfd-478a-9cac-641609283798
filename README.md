# Spring Boot Gateway 透明代理模块

## 项目概述

基于Maven和Spring Boot 2.7.7的透明代理网关模块，用于在前端和后端之间进行完全透明的API请求转发。该网关作为纯代理层，不修改任何请求和响应内容，不添加任何额外功能。



**网络架构**: 前端 → Gateway(8080) → 后端服务(80)

## 技术要求


- **构建工具**: Maven
- **Spring Boot版本**: 2.7.7 (org.springframework.boot)
- **Java版本**: 8
- **项目类型**: Spring Boot应用

### 核心依赖
- spring-boot-starter-web
- spring-boot-starter-webflux

## 项目结构

```
gateway/
├── pom.xml
├── src/
│   └── main/
│       ├── java/
│       │   └── com/
│       │       └── example/
│       │           └── gateway/
│       │               ├── GatewayApplication.java
│       │               ├── config/
│       │               │   └── WebClientConfig.java
│       │               └── controller/
│       │                   └── ProxyController.java
│       └── resources/

│           ├── application.yml
│           └── application-dev.yml
```

## 功能需求




### 1. 透明代理转发

**转发规则**:
- 接收路径：`http://gateway:8080/**`
- 转发目标：`http://backend-server/**`
- 完全保持原始路径结构

### 2. 完全透明转发

#### HTTP方法
- 保持原始HTTP方法不变（GET、POST、PUT、DELETE、PATCH等）

#### 请求头转发
- **完整转发**所有原始请求头
- **不修改**任何请求头内容
- **不添加**任何新的请求头
- 包括但不限于：Content-Type、Authorization、Cookie、User-Agent等

#### 请求体转发
- **原样转发**所有请求体内容
- 支持所有Content-Type：
  - application/json
  - application/x-www-form-urlencoded
  - multipart/form-data
  - text/plain
  - application/octet-stream
  - 其他任意格式

#### 响应转发
- **完整转发**后端响应状态码
- **完整转发**所有响应头
- **原样转发**响应体内容
- **不修改**任何响应数据

### 3. 错误处理

- 后端服务不可用：返回502 Bad Gateway
- 请求超时：返回504 Gateway Timeout
- 连接失败：返回502 Bad Gateway
- **不修改**后端返回的错误响应

## 配置说明

### 配置变量来源

项目使用Spring Boot的配置属性机制，配置变量通过以下方式注入：

1. **配置文件定义**: 在`application.yml`中定义配置属性
2. **属性注入**: 在Java代码中使用`@Value`注解注入配置值

例如在`ProxyController.java`中：
```java
@Value("${gateway.backend.base-url}")
private String backendBaseUrl;

@Value("${gateway.backend.timeout:30000}")
private int timeout;
```

### application.yml
```yaml
server:
  port: 8080                    # Gateway监听端口

gateway:                        # 自定义配置命名空间
  backend:
    base-url: http://localhost  # 后端服务地址，对应 ${gateway.backend.base-url}
    timeout: 30000              # 请求超时时间(毫秒)，对应 ${gateway.backend.timeout}

logging:
  level:
    com.example.gateway: INFO   # 日志级别
```

### application-dev.yml
```yaml
gateway:
  backend:
    base-url: http://localhost  # 开发环境后端地址，会覆盖默认配置
```

### 配置优先级
Spring Boot配置加载优先级（从高到低）：
1. 命令行参数：`--gateway.backend.base-url=http://*************`
2. 环境变量：`GATEWAY_BACKEND_BASE_URL=http://*************`
3. profile特定配置：`application-dev.yml`
4. 默认配置：`application.yml`

## 部署配置

### 网络架构
```
前端应用 → Gateway(8080) → 后端服务(80)
```

### 部署时配置修改

#### 1. 前端项目配置修改
前端项目需要将所有API请求的基础URL修改为Gateway地址：

**修改前（直接访问后端）:**
```javascript
// 前端配置文件中
baseUrl: '/d/',  // 或 'http://localhost/d/'
```

**修改后（通过Gateway访问）:**
```javascript
// 前端配置文件中
baseUrl: 'http://gateway-server:8080/d/',
```

根据前端API列表，需要修改的配置通常在：
- `window.appConfig.baseUrl`
- API服务配置文件
- 环境变量配置

#### 2. Gateway配置修改
修改`application.yml`或通过环境变量配置后端服务地址：



**方式一：修改配置文件**
```yaml
gateway:
  backend:
    base-url: http://backend-server  # 修改为实际后端服务地址
```

**方式二：环境变量**
```bash
export GATEWAY_BACKEND_BASE_URL=http://backend-server
```

**方式三：命令行参数**
```bash
java -jar gateway-1.0.0.jar --gateway.backend.base-url=http://backend-server
```

#### 3. 后端项目配置
后端服务**无需修改任何配置**，保持原有配置不变。


### 部署示例

#### 开发环境
```bash
# 1. 启动C++后端服务（端口80）
# 后端服务正常启动，无需修改

# 2. 启动Gateway（端口8080）
java -jar gateway-1.0.0.jar --gateway.backend.base-url=http://localhost
# 3. 前端配置API地址为Gateway
# baseUrl: 'http://localhost:8080/d/'
```
#### 生产环境
```bash
# 1. C++后端服务部署在服务器（端口80）
# 2. Gateway部署并配置后端地址
java -jar gateway-1.0.0.jar --gateway.backend.base-url=http://backend-server

# 3. 前端配置API地址指向Gateway
# baseUrl: 'http://gateway-server:8080/d/'
```

## 使用方法

### 1. 编译项目
```bash
mvn clean compile
```

### 2. 打包项目
```bash
mvn package -DskipTests
```

### 3. 运行应用
```bash
java -jar target/gateway-1.0.0.jar
```

### 4. 使用开发环境配置
```bash
java -jar target/gateway-1.0.0.jar --spring.profiles.active=dev
```

## 配置对照表

| 组件 | 配置项 | 说明 | 是否需要修改 |
|------|--------|------|-------------|
| **前端** | baseUrl | API请求基础地址 | ✅ **需要修改**<br/>从 `/d/` 改为 `http://gateway:8080/d/` |
| **Gateway** | gateway.backend.base-url | 后端服务地址 | ✅ **需要修改**<br/>设置为实际C++后端服务地址 |
| **Gateway** | server.port | Gateway监听端口 | ❌ 通常不需要修改（默认8080） |
| **C++后端** | 所有配置 | 后端服务配置 | ❌ **无需修改**<br/>保持原有配置 |

## 常见部署场景

### 场景1：本地开发
- C++后端：`http://localhost:80`
- Gateway：`http://localhost:8080`

- 前端baseUrl配置：`http://localhost:8080/d/`

### 场景2：服务器部署
- C++后端服务器：`http://backend-server:80`
- Gateway服务器：`http://gateway-server:8080`
- 前端baseUrl配置：`http://gateway-server:8080/d/`

## 验收标准

1. **透明性**: 前端通过Gateway访问后端与直接访问后端结果完全一致
2. **完整性**: 所有请求头、响应头、请求体、响应体完全保持原样
3. **兼容性**: 支持前端和后端的所有现有API
4. **性能**: Gateway引入的额外延迟<50ms


5. **稳定性**: 长时间运行无内存泄漏，正确处理网络异常

## 关键要求

1. **完全透明**: Gateway对请求和响应不做任何修改
2. **零业务逻辑**: 不包含任何业务相关的处理
3. **高性能**: 最小化转发延迟
4. **稳定可靠**: 处理网络异常，但不修改业务错误
5. **简单维护**: 代码结构简单清晰

## 支持的API路径

基于提供的前端和后端API列表，Gateway需要透明转发所有以下路径：

### 认证相关
- `/d/login` - 用户登录
- `/d/logout` - 用户登出
- `/d/auth` - 用户认证
- `/d/auth_status` - 认证状态查询


### 流量分析
- `/d/feature` - 流量特征提取
- `/d/event_feature` - 事件特征提取
- `/d/event` - 安全事件查询
- `/d/evidence` - 数据包证据提取
- `/d/topn` - TopN统计查询

### 资产管理
- `/d/asset` - 资产查询（IP、服务、主机、URL等）
- `/d/statinfo` - 资产统计信息

### 监控管理
- `/d/mo` - 监控对象管理
- `/d/config` - 系统配置管理
- `/d/bwlist` - 黑白名单管理
- `/d/internalip` - 内网IP管理

### 系统控制
- `/d/sctl` - 系统控制和状态查询
### 工具接口
- `/d/geoinfo` - IP地理位置信息查询
- `/d/portinfo` - 端口服务信息查询
- `/d/ipinfo` - IP详细信息查询
- `/d/threatinfo` - 威胁情报查询
- `/d/threatinfopro` - 威胁情报专业版查询
- `/d/locinfo` - IP位置信息

**重要**: Gateway对所有路径进行**完全透明**的转发，不区分具体的API功能

## API转发示例

网关监听端口8080，接收所有路径的请求：
- 接收路径：`http://gateway:8080/**`
- 转发目标：`http://backend-server/**`

例如：
- 前端请求：`http://gateway:8080/d/login`
- 转发到：`http://backend-server/d/login`

## 验收标准

1. **透明性**: 前端通过Gateway访问后端与直接访问后端结果完全一致
2. **完整性**: 所有请求头、响应头、请求体、响应体完全保持原样
3. **兼容性**: 支持前端和后端的所有现有API
4. **性能**: Gateway引入的额外延迟<50ms
5. **稳定性**: 长时间运行无内存泄漏，正确处理网络异常

## 注意事项
- 该网关为纯代理层，不包含任何业务逻辑
- 不修改任何请求和响应内容
- 确保C++后端服务在配置的地址上可用
- 根据实际环境调整配置文件中的后端服务地址
- 支持前端API列表中的所有53个接口
- 支持后端OpenAPI规范中定义的所有接口
