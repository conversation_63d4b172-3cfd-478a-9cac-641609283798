# 用于批量生成git提交记录的脚本
import pandas as pd
import numpy as np
import paramiko
import os
import json
import random
import datetime
import subprocess
import time
from pathlib import Path

class GitCommitGenerator:
    def __init__(self):
        # 远程服务器配置
        self.remote_host = "**************"  # 需要配置远程服务器IP
        self.remote_user = "root"
        self.remote_password = "QhSnN5kgPM"  # 需要配置密码

        # Git远程仓库配置
        self.git_remote_base_url = "http://**************/r/jh_springboot.git"  # 基础URL，不包含用户名
        self.git_remote_url = "http://yanghuangang@**************/r/jh_springboot.git"  # 默认URL，用于初始化

        # SSH客户端
        self.ssh_client = None

        # ly_server目录的固定路径 - 使用当前目录
        self.ly_server_path = os.getcwd()

        # 时间渐进策略配置 - 2024年1月到4月
        self.start_date = datetime.datetime(2024, 1, 1, 9, 0, 0)  # 2024年1月1日 9:00
        self.end_date = datetime.datetime(2024, 4, 30, 18, 0, 0)  # 2024年4月30日 18:00
        self.current_commit_index = 0  # 当前提交索引

        # 支持的文本文件扩展名（提取为类属性，提高复用性）
        self.text_extensions = {
            # Java项目文件
            '.java', '.class', '.jar',
            # SpringBoot配置文件
            '.properties', '.yml', '.yaml', '.xml',
            # 前端文件
            '.js', '.jsx', '.ts', '.tsx', '.css', '.less', '.scss', '.html', '.htm',
            # 构建文件
            '.gradle', '.maven',
            # SQL文件
            '.sql',
            # 配置文件
            '.json', '.ini', '.conf', '.cfg',
            # 文档文件
            '.md', '.txt', '.rst'
        }
        self.special_files = {'pom.xml', 'build.gradle', 'application.properties', 'application.yml'}

        # Git用户配置
        self.git_users = [
            {"name": "yanghuangang", "email": "<EMAIL>"},
            {"name": "minguoyuan", "email": "<EMAIL>"}
        ]

        # 代码模块分配（使用绝对路径）- 基于SpringBoot项目结构
        self.module_assignment = {
                "yanghuangang": [
                    os.path.join(self.ly_server_path, "src/main/java/com/jinhong/controller"),
                    os.path.join(self.ly_server_path, "src/main/java/com/jinhong/service"),
                    os.path.join(self.ly_server_path, "src/main/java/com/jinhong/entity"),
                    os.path.join(self.ly_server_path, "src/main/java/com/jinhong/config"),
                    os.path.join(self.ly_server_path, "src/main/java/com/jinhong/utils"),
                    os.path.join(self.ly_server_path, "src/main/resources/application.yml"),
                    os.path.join(self.ly_server_path, "src/main/resources/application.properties"),
                    os.path.join(self.ly_server_path, "src/main/resources/static"),
                    os.path.join(self.ly_server_path, "src/main/resources/templates"),
                    os.path.join(self.ly_server_path, "pom.xml")
                ],
                "minguoyuan": [
                    os.path.join(self.ly_server_path, "src/main/java/com/jinhong/dao"),
                    os.path.join(self.ly_server_path, "src/main/java/com/jinhong/mapper"),
                    os.path.join(self.ly_server_path, "src/main/java/com/jinhong/dto"),
                    os.path.join(self.ly_server_path, "src/main/java/com/jinhong/vo"),
                    os.path.join(self.ly_server_path, "src/main/java/com/jinhong/exception"),
                    os.path.join(self.ly_server_path, "src/main/resources/mapper"),
                    os.path.join(self.ly_server_path, "src/test/java"),
                    os.path.join(self.ly_server_path, "src/main/resources/sql"),
                    os.path.join(self.ly_server_path, "src/main/resources/config"),
                    os.path.join(self.ly_server_path, "README.md")
                ]
            }

        # 真实的提交信息模板 - 针对SpringBoot项目
        self.commit_messages = [
            "feat: 添加用户管理模块",
            "fix: 修复数据库连接池配置问题",
            "refactor: 重构Service层代码结构",
            "feat: 实现JWT认证功能",
            "fix: 修复Controller参数校验异常",
            "perf: 优化数据库查询性能",
            "docs: 更新API接口文档",
            "style: 统一代码格式化规范",
            "feat: 添加Redis缓存支持",
            "fix: 修复事务回滚问题",
            "refactor: 优化异常处理机制",
            "feat: 集成Swagger文档",
            "fix: 修复跨域请求配置",
            "perf: 优化分页查询逻辑",
            "feat: 添加日志记录功能",
            "fix: 改善代码",
            "refactor: 抽取公共工具类",
            "feat: 优化代码",
            "fix: 修复定时任务配置",
            "perf: 优化启动速度"
        ]

    def is_text_file(self, file_path):
        """检查文件是否为支持的文本文件类型"""
        file_ext = os.path.splitext(file_path)[1].lower()
        filename = os.path.basename(file_path).lower()
        return file_ext in self.text_extensions or filename in self.special_files

    def check_ly_server_directory(self):
        """检查ly_server目录是否存在"""
        if not os.path.exists(self.ly_server_path):
            print(f"❌ 找不到ly_server目录: {self.ly_server_path}")
            return False

        print(f"✅ ly_server目录检查通过: {self.ly_server_path}")
        return True

    def create_git_env(self, user, commit_time):
        """创建Git环境变量，提高代码复用性"""
        env = os.environ.copy()
        time_str = commit_time.strftime("%Y-%m-%d %H:%M:%S")
        env.update({
            'GIT_AUTHOR_NAME': user['name'],
            'GIT_AUTHOR_EMAIL': user['email'],
            'GIT_COMMITTER_NAME': user['name'],
            'GIT_COMMITTER_EMAIL': user['email'],
            'GIT_AUTHOR_DATE': time_str,
            'GIT_COMMITTER_DATE': time_str
        })
        return env, time_str

    def save_module_assignment(self):
        """保存模块分配到JSON文件"""
        with open('module_assignment.json', 'w', encoding='utf-8') as f:
            json.dump(self.module_assignment, f, ensure_ascii=False, indent=2)
        print("模块分配已保存到 module_assignment.json")

    def connect_remote_server(self):
        """连接远程服务器"""
        try:
            self.ssh_client = paramiko.SSHClient()
            self.ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            self.ssh_client.connect(
                hostname=self.remote_host,
                username=self.remote_user,
                password=self.remote_password,
                timeout=10,
                allow_agent=False,
                look_for_keys=False
            )
            print(f"成功连接到远程服务器 {self.remote_host}")
            return True
        except Exception as e:
            print(f"连接远程服务器失败: {e}")
            return False

    def generate_progressive_work_time(self, total_commits=400):
        
        # 计算总的时间跨度（秒）
        total_seconds = (self.end_date - self.start_date).total_seconds()

        # 计算每个提交之间的平均时间间隔
        interval_seconds = total_seconds / total_commits

        # 计算当前提交的基准时间
        base_time = self.start_date + datetime.timedelta(seconds=self.current_commit_index * interval_seconds)

        # 在基准时间前后添加一些随机性（±1天，避免跨度太大）
        random_offset = random.randint(-1*24*3600, 1*24*3600)  # ±1天的秒数
        target_time = base_time + datetime.timedelta(seconds=random_offset)

        # 确保时间在范围内
        if target_time < self.start_date:
            target_time = self.start_date
        elif target_time > self.end_date:
            target_time = self.end_date

        # 调整到工作时间（9:00-18:00）和工作日
        target_time = self.adjust_to_work_time(target_time)

        # 增加提交索引
        self.current_commit_index += 1

        # 调试信息
        print(f"调试: 提交索引={self.current_commit_index-1}, 基准时间={base_time.strftime('%Y-%m-%d %H:%M:%S')}, 最终时间={target_time.strftime('%Y-%m-%d %H:%M:%S')}")

        return target_time

    def adjust_to_work_time(self, dt):
        """调整时间到工作时间和工作日"""
        # 调整到工作日（周一到周五）
        while dt.weekday() >= 5:  # 周六或周日
            dt = dt + datetime.timedelta(days=1)

        # 调整到工作时间（9:00-18:00）
        if dt.hour < 9:
            dt = dt.replace(hour=9, minute=random.randint(0, 59), second=random.randint(0, 59))
        elif dt.hour >= 18:
            dt = dt.replace(hour=random.randint(9, 17), minute=random.randint(0, 59), second=random.randint(0, 59))
        else:
            # 在工作时间内，保持原有时间但随机化分钟和秒
            dt = dt.replace(minute=random.randint(0, 59), second=random.randint(0, 59))

        return dt



    def generate_progressive_work_time_for_cycle(self, cycle_number, total_cycles):
        """为指定轮次生成渐进的工作时间点"""
        # 第一轮提交从初始化时间后1小时开始
        first_commit_time = self.start_date + datetime.timedelta(hours=1)
        first_commit_time = self.adjust_to_work_time(first_commit_time)

        # 计算从第一轮提交时间到结束时间的跨度
        total_seconds = (self.end_date - first_commit_time).total_seconds()

        # 计算每轮之间的平均时间间隔
        interval_seconds = total_seconds / (total_cycles - 1)  # 减1确保最后一轮正好到结束时间

        # 计算当前轮次的基准时间（cycle_number从1开始）
        base_time = first_commit_time + datetime.timedelta(seconds=(cycle_number - 1) * interval_seconds)

        # 在基准时间前后添加一些随机性（±6小时，避免跨度太大）
        random_offset = random.randint(-6*3600, 6*3600)  # ±6小时的秒数
        target_time = base_time + datetime.timedelta(seconds=random_offset)

        # 确保时间在范围内
        if target_time < first_commit_time:
            target_time = first_commit_time
        elif target_time > self.end_date:
            target_time = self.end_date

        # 调整到工作时间（9:00-18:00）和工作日
        target_time = self.adjust_to_work_time(target_time)

        return target_time

    def set_remote_time(self, target_time):
        """设置远程服务器时间 - 使用字符串拼接确保命令格式正确"""
        try:
            # 格式化时间字符串
            year = target_time.strftime("%Y")
            month = target_time.strftime("%m")
            day = target_time.strftime("%d")
            hour = target_time.strftime("%H")
            minute = target_time.strftime("%M")
            second = target_time.strftime("%S")

            # 拼接完整的时间字符串
            time_string = f"{year}-{month}-{day} {hour}:{minute}:{second}"

            # 构造命令 - 确保格式为: date -s "2020-01-01 10:00:00"
            command = 'date -s "' + time_string + '"'

            print(f"🕐 设置远程服务器时间为: {time_string}")
            print(f"📝 执行命令: {command}")

            # 执行命令
            stdin, stdout, stderr = self.ssh_client.exec_command(command, timeout=15)
            result = stdout.read().decode('utf-8', errors='ignore').strip()
            error = stderr.read().decode('utf-8', errors='ignore').strip()

            print(f"命令输出: {result}")
            if error:
                print(f"错误信息: {error}")
                return False

            # 验证时间设置是否成功
            print("🔍 验证时间设置...")
            verify_stdin, verify_stdout, verify_stderr = self.ssh_client.exec_command("date", timeout=10)
            verify_result = verify_stdout.read().decode('utf-8', errors='ignore').strip()

            print(f"✅ 远程服务器当前时间: {verify_result}")
            return True

        except Exception as e:
            print(f"❌ 设置远程时间异常: {e}")
            return False

    def get_remote_time(self):
        """获取远程服务器时间"""
        try:
            stdin, stdout, stderr = self.ssh_client.exec_command("date", timeout=10)
            result = stdout.read().decode('utf-8', errors='ignore').strip()
            error = stderr.read().decode('utf-8', errors='ignore').strip()

            if error:
                print(f"获取远程时间出错: {error}")
                return None

            print(f"🕐 远程服务器当前时间: {result}")
            return result
        except Exception as e:
            print(f"❌ 获取远程时间异常: {e}")
            return None



    def modify_file_with_empty_lines(self, file_path, action='add'):
        """通过添加或删除空行来修改文件"""
        try:
            # 检查路径是否存在
            if not os.path.exists(file_path):
                print(f"路径不存在，跳过: {file_path}")
                return False

            # 如果是目录，跳过
            if os.path.isdir(file_path):
                print(f"是目录，跳过: {file_path}")
                return False

            # 检查文件权限
            if not os.access(file_path, os.R_OK | os.W_OK):
                print(f"文件权限不足，跳过: {file_path}")
                return False

            # 只处理支持的文本文件类型
            if not self.is_text_file(file_path):
                print(f"非文本文件，跳过: {file_path}")
                return False

            # 读取文件内容
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()

            # 检测原文件的换行符类型
            if '\r\n' in content:
                line_ending = '\r\n'  # Windows
            elif '\n' in content:
                line_ending = '\n'    # Unix/Linux/Mac
            else:
                line_ending = '\n'    # 默认使用Unix换行符

            lines = content.splitlines(keepends=True)

            # 如果文件为空或太小，跳过
            if len(lines) < 2:
                print(f"文件内容太少，跳过: {file_path}")
                return False

            if action == 'add':
                # 随机位置添加1-2个空行
                insert_pos = random.randint(1, max(1, len(lines) - 1))  # 避免在文件开头或结尾
                empty_lines_count = random.randint(1, 2)
                for i in range(empty_lines_count):
                    lines.insert(insert_pos, line_ending)
                print(f"在 {os.path.basename(file_path)} 第{insert_pos}行添加了{empty_lines_count}个空行")

            elif action == 'remove':
                # 删除随机的空行
                empty_line_indices = [i for i, line in enumerate(lines) if line.strip() == '']
                # 至少需要2个空行才能删除（保留至少1个空行）
                if empty_line_indices and len(empty_line_indices) >= 2:
                    remove_count = min(random.randint(1, 2), len(empty_line_indices) - 1)
                    indices_to_remove = random.sample(empty_line_indices, remove_count)
                    # 从后往前删除，避免索引变化
                    for idx in sorted(indices_to_remove, reverse=True):
                        lines.pop(idx)
                    print(f"从 {os.path.basename(file_path)} 删除了{remove_count}个空行")
                else:
                    # 没有足够空行可删除时，自动改为添加空行策略
                    empty_count = len(empty_line_indices)
                    print(f"{os.path.basename(file_path)} 只有{empty_count}个空行，不足以删除，改为添加空行")
                    return self.modify_file_with_empty_lines(file_path, 'add')

            # 写回文件，保持原有的换行符格式
            with open(file_path, 'w', encoding='utf-8', newline='') as f:
                f.writelines(lines)

            return True
        except PermissionError:
            print(f"权限不足，跳过: {os.path.basename(file_path)}")
            return False
        except UnicodeDecodeError:
            print(f"编码错误，跳过: {os.path.basename(file_path)}")
            return False
        except Exception as e:
            print(f"修改文件失败，跳过 {os.path.basename(file_path)}: {e}")
            return False

    def git_commit(self, user, message, commit_time):
        """执行git提交"""
        try:
            # 检查是否有文件需要提交
            status_result = subprocess.run(['git', 'status', '--porcelain'],
                                         cwd=self.ly_server_path, capture_output=True, text=True)
            if not status_result.stdout.strip():
                print(f"用户 {user['name']} 没有文件需要提交，跳过")
                return False

            # 添加所有修改的文件
            subprocess.run(['git', 'add', '.'], cwd=self.ly_server_path, check=True)

            # 创建Git环境变量和作者信息
            env, time_str = self.create_git_env(user, commit_time)
            author_info = f"{user['name']} <{user['email']}>"

            # 提交时使用环境变量和--author参数双重保证
            subprocess.run([
                'git', 'commit',
                '-m', message,
                '--author', author_info,
                '--date', time_str
            ], cwd=self.ly_server_path, check=True, env=env)

            print(f"✅ 用户 {user['name']} 在 {time_str} 提交: {message}")

            # 验证提交的作者信息
            verify_result = subprocess.run(['git', 'log', '-1', '--pretty=format:%an <%ae>'],
                                         cwd=self.ly_server_path, capture_output=True, text=True)
            if verify_result.returncode == 0:
                actual_author = verify_result.stdout.strip()
                expected_author = author_info
                if actual_author == expected_author:
                    print(f"🔍 作者验证成功: {actual_author}")
                else:
                    print(f"⚠️ 作者验证失败: 期望 {expected_author}, 实际 {actual_author}")

            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ Git提交失败: {e}")
            return False
        except Exception as e:
            print(f"❌ Git提交异常: {e}")
            return False

    def init_git_repository(self):
        """初始化Git仓库并设置远程服务器时间"""
        try:
            # 初始化Git仓库
            subprocess.run(['git', 'init'], cwd=self.ly_server_path, check=True)
            print("✅ Git仓库初始化成功")

            # 设置初始时间到远程服务器
            initial_time = self.start_date
            initial_time = self.adjust_to_work_time(initial_time)

            print("🕐 为新仓库设置远程服务器初始时间...")
            if not self.connect_remote_server():
                print("❌ 无法连接远程服务器，Git仓库初始化失败")
                return False

            if not self.set_remote_time(initial_time):
                print("❌ 设置远程时间失败，Git仓库初始化失败")
                self.ssh_client.close()
                return False

            print(f"✅ 远程服务器时间已设置为: {initial_time.strftime('%Y-%m-%d %H:%M:%S')}")
            self.get_remote_time()
            self.ssh_client.close()

            return True
        except subprocess.CalledProcessError as e:
            print(f"Git仓库初始化失败: {e}")
            return False

    def setup_git_remote(self):
        """设置Git远程仓库"""
        try:
            # 检查是否已存在origin远程仓库
            result = subprocess.run(['git', 'remote', 'get-url', 'origin'],
                                  cwd=self.ly_server_path, capture_output=True, text=True)

            if result.returncode == 0:
                # 如果存在，更新URL
                subprocess.run(['git', 'remote', 'set-url', 'origin', self.git_remote_url],
                             cwd=self.ly_server_path, check=True)
                print(f"更新远程仓库URL: {self.git_remote_url}")
            else:
                # 如果不存在，添加远程仓库
                subprocess.run(['git', 'remote', 'add', 'origin', self.git_remote_url],
                             cwd=self.ly_server_path, check=True)
                print(f"添加远程仓库: {self.git_remote_url}")

            return True
        except subprocess.CalledProcessError as e:
            print(f"设置远程仓库失败: {e}")
            return False

    def get_user_remote_url(self, user):
        """根据用户生成对应的远程仓库URL"""
        return f"http://{user['name']}@**************/r/jh_springboot.git"

    def update_remote_url_for_user(self, user):
        """为指定用户更新远程仓库URL"""
        try:
            user_remote_url = self.get_user_remote_url(user)
            subprocess.run(['git', 'remote', 'set-url', 'origin', user_remote_url],
                         cwd=self.ly_server_path, check=True)
            print(f"🔗 更新远程仓库URL为用户 {user['name']}: {user_remote_url}")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ 更新远程仓库URL失败: {e}")
            return False

    def push_to_remote(self, user=None, force=False, push_time=None):
        """推送到远程仓库"""
        try:
            # 如果指定了用户，先更新远程URL
            if user:
                if not self.update_remote_url_for_user(user):
                    return False
                print(f"🚀 用户 {user['name']} 正在推送到远程仓库...")
            else:
                print("🚀 正在推送到远程仓库...")

            # 设置推送环境变量
            env = os.environ.copy()
            if push_time and user:
                env, time_str = self.create_git_env(user, push_time)
                print(f"🕐 设置推送时间: {time_str}")
            elif push_time:
                time_str = push_time.strftime("%Y-%m-%d %H:%M:%S")
                env.update({'GIT_COMMITTER_DATE': time_str, 'GIT_AUTHOR_DATE': time_str})
                print(f"🕐 设置推送时间: {time_str}")

            push_cmd = ['git', 'push', 'origin', 'master']
            if force:
                push_cmd.append('--force')
            subprocess.run(push_cmd, cwd=self.ly_server_path, check=True, env=env)

            if user:
                print(f"✅ 用户 {user['name']} 推送成功！")
            else:
                print("✅ 推送成功！")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ 推送失败: {e}")
            # 尝试推送到main分支
            try:
                push_cmd = ['git', 'push', 'origin', 'main']
                if force:
                    push_cmd.append('--force')
                subprocess.run(push_cmd, cwd=self.ly_server_path, check=True)

                if user:
                    print(f"✅ 用户 {user['name']} 推送到main分支成功！")
                else:
                    print("✅ 推送到main分支成功！")
                return True
            except subprocess.CalledProcessError as e2:
                print(f"❌ 推送到main分支也失败: {e2}")
                return False

    def initial_commit_and_push(self):
        """初始提交并推送所有文件到远程仓库"""
        print("\n🚀 执行初始化推送...")
        try:
            # 使用第一个用户作为初始提交用户
            default_user = self.git_users[0]
            author_info = f"{default_user['name']} <{default_user['email']}>"
            print(f"设置初始Git用户: {author_info}")

            # 生成初始提交时间（2020年第一个工作日）
            initial_time = self.start_date
            initial_time = self.adjust_to_work_time(initial_time)

            # 连接远程服务器并设置初始时间（强制要求）
            print("🕐 设置远程服务器初始时间...")
            if not self.connect_remote_server():
                print("❌ 无法连接远程服务器，初始化失败")
                return False

            if not self.set_remote_time(initial_time):
                print("❌ 设置远程时间失败，初始化失败")
                self.ssh_client.close()
                return False

            print(f"✅ 远程服务器时间已设置为: {initial_time.strftime('%Y-%m-%d %H:%M:%S')}")
            self.get_remote_time()  # 确认时间设置
            self.ssh_client.close()
            env, time_str = self.create_git_env(default_user, initial_time)

            # 检查是否有文件需要提交
            result = subprocess.run(['git', 'status', '--porcelain'],
                                  cwd=self.ly_server_path, capture_output=True, text=True, check=True)

            need_commit = bool(result.stdout.strip())
            if not need_commit:
                # 检查是否有提交历史
                try:
                    subprocess.run(['git', 'log', '--oneline', '-1'],
                                 cwd=self.ly_server_path, capture_output=True, check=True)
                    print("仓库已有提交历史，尝试推送现有内容...")
                except subprocess.CalledProcessError:
                    need_commit = True
                    print("仓库没有任何提交，创建初始提交...")

            if need_commit:
                print("创建初始提交...")
                # 添加所有文件并提交
                subprocess.run(['git', 'add', '.'], cwd=self.ly_server_path, check=True)
                subprocess.run(['git', 'commit', '-m', 'Initial commit', '--author', author_info, '--date', time_str],
                             cwd=self.ly_server_path, check=True, env=env)
                print(f"创建初始提交，时间: {time_str}，作者: {author_info}")

            # 推送到远程仓库，使用默认用户身份和相同的时间
            print("推送初始内容到远程仓库...")
            if self.push_to_remote(user=default_user, force=True, push_time=initial_time):
                print("✅ 初始化推送成功！")
                return True
            else:
                print("❌ 初始化推送失败")
                return False

        except subprocess.CalledProcessError as e:
            print(f"初始化推送过程中出错: {e}")
            return False

    def find_modifiable_files(self, user_files):
        """从用户文件列表中找到可修改的文件"""
        modifiable_files = []

        for file_path in user_files:
            try:
                # 检查路径是否存在
                if not os.path.exists(file_path):
                    continue

                # 如果是目录，遍历其中的文件
                if os.path.isdir(file_path):
                    for root, dirs, files in os.walk(file_path):
                        for file in files:
                            full_path = os.path.join(root, file)
                            if self.is_text_file(full_path) and os.access(full_path, os.R_OK | os.W_OK):
                                modifiable_files.append(full_path)
                else:
                    # 检查单个文件
                    if self.is_text_file(file_path) and os.access(file_path, os.R_OK | os.W_OK):
                        modifiable_files.append(file_path)
            except Exception:
                continue

        return modifiable_files

    def generate_single_commit_for_user(self, user_index, commit_time):
        """为指定用户生成单个提交记录"""
        user = self.git_users[user_index]
        user_files = self.module_assignment[user['name']]

        try:
            # 找到可修改的文件
            modifiable_files = self.find_modifiable_files(user_files)

            if not modifiable_files:
                print(f"用户 {user['name']} 没有可修改的文件，跳过本次提交")
                return False

            # 最多尝试3次找到可修改的文件
            max_attempts = 3
            for attempt in range(max_attempts):
                # 随机选择要修改的文件
                file_to_modify = random.choice(modifiable_files)

                # 随机选择操作（添加或删除空行）
                action = random.choice(['add', 'remove'])

                # 修改文件
                if self.modify_file_with_empty_lines(file_to_modify, action):
                    # 随机选择提交信息
                    commit_message = random.choice(self.commit_messages)

                    # 执行git提交
                    if self.git_commit(user, commit_message, commit_time):
                        print(f"用户 {user['name']} 提交成功: {commit_message}")
                        return True
                    else:
                        print(f"用户 {user['name']} git提交失败，尝试下一个文件")
                        continue
                else:
                    print(f"用户 {user['name']} 文件修改失败，尝试下一个文件")
                    # 从可修改文件列表中移除失败的文件
                    if file_to_modify in modifiable_files:
                        modifiable_files.remove(file_to_modify)
                    if not modifiable_files:
                        break
                    continue

            print(f"用户 {user['name']} 尝试{max_attempts}次后仍然失败")
            return False

        except Exception as e:
            print(f"用户 {user['name']} 提交异常: {e}")
            return False

    def batch_commit_and_push_cycle(self, cycle_number, total_cycles):
        """单次循环：设置时间 -> 两个用户各自提交推送"""
        print(f"\n{'='*60}")
        print(f"第 {cycle_number}/{total_cycles} 轮提交推送")
        print(f"{'='*60}")

        # 生成渐进时间（每轮生成一个时间，2个用户共用）
        commit_time = self.generate_progressive_work_time_for_cycle(cycle_number, total_cycles)
        print(f"本轮提交时间: {commit_time.strftime('%Y-%m-%d %H:%M:%S')} (第{cycle_number}轮)")

        successful_commits = 0

        # 随机化用户提交顺序
        user_indices = list(range(len(self.git_users)))
        random.shuffle(user_indices)
        print(f"本轮用户提交顺序: {[self.git_users[i]['name'] for i in user_indices]}")

        # 两个用户按随机顺序提交
        for i in user_indices:
            user = self.git_users[i]
            print(f"\n--- 用户 {user['name']} 开始提交 ---")

            # 每个用户提交前，重新连接并设置远程服务器时间（强制要求）
            if not self.connect_remote_server():
                print(f"❌ 无法连接远程服务器，第{cycle_number}轮失败")
                return False

            if not self.set_remote_time(commit_time):
                print(f"❌ 无法设置远程时间，第{cycle_number}轮失败")
                self.ssh_client.close()
                return False

            # 确认远程时间已设置
            self.get_remote_time()
            self.ssh_client.close()

            if self.generate_single_commit_for_user(i, commit_time):
                # 立即推送该用户的提交，使用该用户的身份和相同的时间
                if self.push_to_remote(user=user, push_time=commit_time):
                    print(f"✅ 用户 {user['name']} 提交推送成功")
                    successful_commits += 1
                else:
                    print(f"❌ 用户 {user['name']} 推送失败")
            else:
                print(f"⚠️ 用户 {user['name']} 提交失败，跳过")

            # 短暂延迟
            time.sleep(1)

        print(f"\n本轮完成: {successful_commits}/2 个用户成功提交推送")
        # 修改成功条件：至少有一个用户成功即可继续
        return successful_commits > 0

    def run_batch_commits(self, total_cycles=100):
        """批量生成所有用户的提交记录 - 新的推送策略"""
        print("开始批量生成Git提交记录...")
        print(f"推送策略: 每轮更改远程时间 -> 2个用户各自提交推送，共{total_cycles}轮")

        # 保存模块分配
        self.save_module_assignment()

        # 检查是否在git仓库中，如果不是则自动初始化
        try:
            subprocess.run(['git', 'status'], cwd=self.ly_server_path, check=True, capture_output=True)
            print("✅ Git仓库检查通过")
        except subprocess.CalledProcessError:
            print(f"📁 {self.ly_server_path} 不是git仓库，正在自动初始化...")
            if not self.init_git_repository():
                print("❌ Git仓库初始化失败，程序退出")
                return False

        # 设置远程仓库
        if not self.setup_git_remote():
            print("设置远程仓库失败，程序退出")
            return False

        # 执行初始化推送
        print("\n📋 检查远程仓库状态...")
        if not self.initial_commit_and_push():
            print("初始化推送失败，程序退出")
            return False

        successful_cycles = 0
        total_commits = 0

        # 执行100轮循环
        for cycle in range(1, total_cycles + 1):
            try:
                cycle_result = self.batch_commit_and_push_cycle(cycle, total_cycles)
                if cycle_result:
                    successful_cycles += 1

                # 统计本轮实际提交数（通过检查git log）
                try:
                    result = subprocess.run(['git', 'rev-list', '--count', 'HEAD'],
                                          cwd=self.ly_server_path, capture_output=True, text=True)
                    if result.returncode == 0:
                        current_total = int(result.stdout.strip())
                        if cycle == 1:
                            initial_commits = current_total
                            total_commits = 0
                        else:
                            total_commits = current_total - initial_commits
                except:
                    # 如果无法获取准确数量，使用估算
                    total_commits += successful_cycles * 2  # 保守估计每轮平均2个提交

                # 每10轮显示一次进度
                if cycle % 10 == 0:
                    print(f"\n🎯 进度报告: 已完成 {cycle}/{total_cycles} 轮")
                    print(f"   成功轮次: {successful_cycles}")
                    print(f"   总提交数: {total_commits}")

                # 轮次间短暂延迟
                time.sleep(2)

            except KeyboardInterrupt:
                print(f"\n用户中断，已完成 {cycle-1} 轮")
                break
            except Exception as e:
                print(f"第 {cycle} 轮执行异常: {e}")
                continue

        print(f"\n{'='*60}")
        print(f"🎉 批量提交推送完成！")
        print(f"   总轮次: {total_cycles}")
        print(f"   成功轮次: {successful_cycles}")
        print(f"   总提交数: {total_commits}")
        print(f"   模块分配信息已保存到 module_assignment.json")
        print(f"{'='*60}")

        return True

    def configure_remote_server(self, host, password):
        """配置远程服务器信息"""
        self.remote_host = host
        self.remote_password = password
        print(f"远程服务器配置完成: {host}")

def main():
    """主函数"""
    generator = GitCommitGenerator()

    # 检查ly_server目录
    if not generator.check_ly_server_directory():
        return

    # 生成随机轮数（100-120轮之间）
    total_cycles = random.randint(100, 120)
    print(f"\n🎲 随机生成轮数: {total_cycles}轮")
    print(f"📊 预计总提交数: {total_cycles * 2}个 (2用户 × {total_cycles}轮)")

    # 确认开始执行
    confirm = input("\n确认开始生成提交记录？(y/N): ").strip().lower()
    if confirm != 'y':
        print("操作已取消")
        return

    # 执行批量提交
    generator.run_batch_commits(total_cycles)

if __name__ == "__main__":
    main()
